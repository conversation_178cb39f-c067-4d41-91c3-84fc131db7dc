#!/usr/bin/env python3

import re
from collections import Counter, defaultdict

def load_encrypted_text(filename):
    """Load encrypted text from file"""
    with open(filename, 'r') as f:
        return f.read().strip()

def analyze_patterns(text):
    """Analyze patterns in the encrypted text"""
    words = text.lower().split()
    
    # Find single letter words (likely 'a' or 'i')
    single_letters = [w for w in words if len(w) == 1]
    
    # Find two letter words
    two_letters = [w for w in words if len(w) == 2]
    
    # Find three letter words
    three_letters = [w for w in words if len(w) == 3]
    
    # Count letter frequencies
    letter_freq = Counter(c for c in text.lower() if c.isalpha())
    
    return {
        'single_letters': Counter(single_letters),
        'two_letters': Counter(two_letters),
        'three_letters': Counter(three_letters),
        'letter_freq': letter_freq,
        'words': words
    }

def find_common_patterns(words):
    """Find common word patterns that might help identify mappings"""
    patterns = defaultdict(list)
    
    for word in words:
        if len(word) <= 10:  # Focus on shorter words
            # Create pattern based on repeated letters
            pattern = []
            seen = {}
            next_num = 0
            for char in word:
                if char not in seen:
                    seen[char] = next_num
                    next_num += 1
                pattern.append(str(seen[char]))
            pattern_str = ''.join(pattern)
            patterns[pattern_str].append(word)
    
    return patterns

def create_mapping_from_guesses(guesses):
    """Create a mapping dictionary from character guesses"""
    mapping = {}
    reverse_mapping = {}
    
    for encrypted, decrypted in guesses.items():
        if encrypted in mapping and mapping[encrypted] != decrypted:
            print(f"Conflict: {encrypted} mapped to both {mapping[encrypted]} and {decrypted}")
        if decrypted in reverse_mapping and reverse_mapping[decrypted] != encrypted:
            print(f"Conflict: {decrypted} mapped from both {reverse_mapping[decrypted]} and {encrypted}")
        
        mapping[encrypted] = decrypted
        reverse_mapping[decrypted] = encrypted
    
    return mapping

def decrypt_text(text, mapping):
    """Decrypt text using the mapping"""
    result = ""
    for char in text:
        if char.lower() in mapping:
            decrypted = mapping[char.lower()]
            result += decrypted.upper() if char.isupper() else decrypted
        else:
            result += char
    return result

def main():
    # Load the encrypted study guide
    study_guide = load_encrypted_text('study-guide.txt')
    secret = load_encrypted_text('secret.txt')
    
    # Analyze patterns
    analysis = analyze_patterns(study_guide)
    patterns = find_common_patterns(analysis['words'])
    
    print("Letter frequency analysis:")
    for letter, count in analysis['letter_freq'].most_common(10):
        print(f"{letter}: {count}")
    
    print("\nSingle letter words:", analysis['single_letters'])
    print("Two letter words:", analysis['two_letters'].most_common(10))
    print("Three letter words:", analysis['three_letters'].most_common(10))
    
    # Common English letter frequencies (approximate)
    english_freq = ['e', 't', 'a', 'o', 'i', 'n', 's', 'h', 'r', 'd', 'l', 'u']
    
    # Start with frequency-based guesses
    encrypted_freq = [letter for letter, count in analysis['letter_freq'].most_common()]
    
    print(f"\nMost frequent encrypted letters: {encrypted_freq[:12]}")
    print(f"Expected English frequencies:    {english_freq}")
    
    # Manual analysis based on common patterns
    # Let's look for common English words and their patterns
    
    # Look for words that might be "the" (pattern 012)
    the_candidates = patterns.get('012', [])
    print(f"\nPossible 'the' candidates (pattern 012): {the_candidates[:10]}")
    
    # Look for words that might be "and" (pattern 012)
    and_candidates = patterns.get('012', [])
    print(f"Possible 'and' candidates (pattern 012): {and_candidates[:10]}")
    
    # Look for words that might be "that" (pattern 0120)
    that_candidates = patterns.get('0120', [])
    print(f"Possible 'that' candidates (pattern 0120): {that_candidates[:10]}")
    
    # Start building mapping based on analysis
    # This will require some manual work and iteration
    
    # Let's try some common word mappings
    # Based on frequency analysis, let's make some educated guesses
    
    # Most frequent letters in encrypted text vs English
    initial_guesses = {}
    
    # 'x' appears very frequently - likely 'e'
    # 'o' appears frequently - likely 't' or 'a'
    # 'i' appears frequently - likely 'i' or 'n'
    
    # Let's start with some common patterns
    # If we see repeated patterns, we can make educated guesses
    
    print(f"\nSample encrypted words: {analysis['words'][:20]}")
    
    # Let's try to identify some mappings manually
    # This is where we'd need to do more detailed analysis
    
    # For now, let's try a simple frequency-based mapping
    mapping = {}
    for i, (enc_letter, _) in enumerate(analysis['letter_freq'].most_common()):
        if i < len(english_freq):
            mapping[enc_letter] = english_freq[i]
    
    print(f"\nInitial frequency-based mapping:")
    for enc, dec in sorted(mapping.items()):
        print(f"{enc} -> {dec}")
    
    # Try decrypting with this mapping
    print(f"\nDecrypted secret (frequency-based): {decrypt_text(secret, mapping)}")
    print(f"\nFirst few decrypted study guide words: {decrypt_text(' '.join(analysis['words'][:10]), mapping)}")

if __name__ == "__main__":
    main()
